<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}SSO Admin Panel{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stat-card-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .stat-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
        }
        
        .table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .alert {
            border: none;
            border-radius: 15px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 25px 25px;
        }

        /* Enhanced Navigation Styles */
        .sidebar .nav-link {
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 2px 0;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 8px;
        }

        .sidebar .collapse .nav-link {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .sidebar .collapse .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .collapse .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
        }

        .sidebar-divider {
            border-color: rgba(255, 255, 255, 0.2);
        }

        .bi-chevron-down {
            transition: transform 0.3s ease;
        }

        .nav-link[aria-expanded="true"] .bi-chevron-down {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            {% if session.token %}
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white"><i class="bi bi-shield-lock"></i> SSO Admin</h4>
                        <small class="text-white-50">Welcome, {{ session.user.username or 'Admin' }}</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>

                        <!-- User Management -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint in ['users', 'create_user', 'edit_user', 'user_profile'] %}active{% endif %}"
                               href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <i class="bi bi-people"></i> User Management
                                <i class="bi bi-chevron-down float-end"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['users', 'create_user', 'edit_user', 'user_profile'] %}show{% endif %}" id="userSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'users' %}active{% endif %}" href="{{ url_for('users') }}">
                                            <i class="bi bi-list-ul"></i> All Users
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'create_user' %}active{% endif %}" href="{{ url_for('edit_user', user_id=0) }}">
                                            <i class="bi bi-person-plus"></i> Create User
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showBulkActions('users')">
                                            <i class="bi bi-gear"></i> Bulk Actions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Application Management -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint in ['applications', 'create_application', 'edit_application'] %}active{% endif %}"
                               href="#appSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <i class="bi bi-app"></i> Applications
                                <i class="bi bi-chevron-down float-end"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['applications', 'create_application', 'edit_application'] %}show{% endif %}" id="appSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'applications' %}active{% endif %}" href="{{ url_for('applications') }}">
                                            <i class="bi bi-list-ul"></i> All Applications
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showCreateApplicationModal()">
                                            <i class="bi bi-plus-circle"></i> Create Application
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showBulkActions('applications')">
                                            <i class="bi bi-gear"></i> Bulk Actions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Security & Access -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint in ['roles', 'permissions', 'create_role', 'edit_role', 'create_permission'] %}active{% endif %}"
                               href="#securitySubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <i class="bi bi-shield-lock"></i> Security & Access
                                <i class="bi bi-chevron-down float-end"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['roles', 'permissions', 'create_role', 'edit_role', 'create_permission'] %}show{% endif %}" id="securitySubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'roles' %}active{% endif %}" href="{{ url_for('roles') }}">
                                            <i class="bi bi-person-badge"></i> Roles
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'permissions' %}active{% endif %}" href="{{ url_for('permissions') }}">
                                            <i class="bi bi-key"></i> Permissions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Organization Structure -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint in ['branches', 'departments', 'positions', 'organization'] %}active{% endif %}"
                               href="#orgSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <i class="bi bi-building"></i> Organization
                                <i class="bi bi-chevron-down float-end"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['branches', 'departments', 'positions', 'organization'] %}show{% endif %}" id="orgSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'branches' %}active{% endif %}" href="#" onclick="loadPage('branches')">
                                            <i class="bi bi-geo-alt"></i> Branches
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'departments' %}active{% endif %}" href="#" onclick="loadPage('departments')">
                                            <i class="bi bi-diagram-3"></i> Departments
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'positions' %}active{% endif %}" href="#" onclick="loadPage('positions')">
                                            <i class="bi bi-briefcase"></i> Positions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Analytics & Reports -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint in ['analytics', 'reports', 'system_stats', 'user_stats'] %}active{% endif %}"
                               href="#analyticsSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <i class="bi bi-graph-up"></i> Analytics & Reports
                                <i class="bi bi-chevron-down float-end"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['analytics', 'reports', 'system_stats', 'user_stats'] %}show{% endif %}" id="analyticsSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'system_stats' %}active{% endif %}" href="#" onclick="loadPage('system_stats')">
                                            <i class="bi bi-bar-chart"></i> System Statistics
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'user_stats' %}active{% endif %}" href="#" onclick="loadPage('user_stats')">
                                            <i class="bi bi-people-fill"></i> User Analytics
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'activities' %}active{% endif %}" href="#" onclick="loadPage('activities')">
                                            <i class="bi bi-clock-history"></i> Activity Logs
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="generateReport()">
                                            <i class="bi bi-file-earmark-text"></i> Generate Report
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- System Settings -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint in ['settings', 'system_config', 'oauth_settings'] %}active{% endif %}"
                               href="#settingsSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <i class="bi bi-gear"></i> System Settings
                                <i class="bi bi-chevron-down float-end"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['settings', 'system_config', 'oauth_settings'] %}show{% endif %}" id="settingsSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'system_config' %}active{% endif %}" href="#" onclick="loadPage('system_config')">
                                            <i class="bi bi-sliders"></i> System Configuration
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'oauth_settings' %}active{% endif %}" href="#" onclick="loadPage('oauth_settings')">
                                            <i class="bi bi-shield-check"></i> OAuth Settings
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showSystemHealth()">
                                            <i class="bi bi-heart-pulse"></i> System Health
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Divider -->
                        <hr class="sidebar-divider my-3">

                        <!-- User Account -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'profile' %}active{% endif %}" href="#" onclick="loadPage('profile')">
                                <i class="bi bi-person-circle"></i> My Profile
                            </a>
                        </li>

                        <!-- Logout -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            {% endif %}
            
            <!-- Main content -->
            <main class="{% if session.token %}col-md-9 ms-sm-auto col-lg-10 px-md-4{% else %}col-12{% endif %} main-content">
                {% if not session.token %}
                <div class="page-header text-center">
                    <h1><i class="bi bi-shield-lock"></i> SSO Admin Panel</h1>
                    <p class="lead">Secure Single Sign-On Administration</p>
                </div>
                {% endif %}
                
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="mt-3">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    <i class="bi bi-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-triangle{% elif category == 'warning' %}exclamation-circle{% else %}info-circle{% endif %}"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}
                
                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Confirm delete actions
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('[data-confirm-delete]');
            deleteButtons.forEach(function(button) {
                button.addEventListener('click', function(e) {
                    if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                        e.preventDefault();
                    }
                });
            });
        });

        // Enhanced Navigation Functions
        function loadPage(page) {
            // Show loading indicator
            showLoadingIndicator();

            // Map page names to actual routes
            const pageRoutes = {
                'branches': '/organization/branches',
                'departments': '/organization/departments',
                'positions': '/organization/positions',
                'system_stats': '/analytics/system-stats',
                'user_stats': '/analytics/user-stats',
                'activities': '/analytics/activities',
                'system_config': '/settings/system',
                'oauth_settings': '/settings/oauth',
                'profile': '/profile'
            };

            const route = pageRoutes[page];
            if (route) {
                window.location.href = route;
            } else {
                // For now, show a placeholder message
                showPlaceholderMessage(page);
            }
        }

        function showPlaceholderMessage(page) {
            hideLoadingIndicator();
            const pageName = page.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            alert(`${pageName} page is coming soon! This feature will be implemented in the next update.`);
        }

        function showBulkActions(type) {
            const actionType = type === 'users' ? 'User' : 'Application';
            const actions = type === 'users'
                ? ['Activate', 'Deactivate', 'Verify', 'Delete', 'Unlock']
                : ['Activate', 'Deactivate', 'Delete'];

            let actionList = actions.map(action => `<option value="${action.toLowerCase()}">${action}</option>`).join('');

            const modalHtml = `
                <div class="modal fade" id="bulkActionModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Bulk ${actionType} Actions</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>Select an action to perform on multiple ${type}:</p>
                                <select class="form-select" id="bulkActionSelect">
                                    <option value="">Choose an action...</option>
                                    ${actionList}
                                </select>
                                <div class="mt-3">
                                    <small class="text-muted">Note: You'll be able to select specific ${type} after choosing an action.</small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" onclick="executeBulkAction('${type}')">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('bulkActionModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('bulkActionModal'));
            modal.show();
        }

        function executeBulkAction(type) {
            const action = document.getElementById('bulkActionSelect').value;
            if (!action) {
                alert('Please select an action first.');
                return;
            }

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('bulkActionModal'));
            modal.hide();

            // Navigate to the appropriate page with bulk action mode
            const targetPage = type === 'users' ? '/users' : '/applications';
            window.location.href = `${targetPage}?bulk_action=${action}`;
        }

        function showCreateApplicationModal() {
            // For now, redirect to applications page
            // In the future, this could open a modal for quick app creation
            window.location.href = '/applications';
        }

        function generateReport() {
            showLoadingIndicator();

            // Simulate report generation
            setTimeout(() => {
                hideLoadingIndicator();
                alert('Report generation feature is coming soon! This will allow you to export system data and analytics.');
            }, 1000);
        }

        function showSystemHealth() {
            showLoadingIndicator();

            // Make API call to check system health
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    hideLoadingIndicator();
                    showSystemHealthModal(data);
                })
                .catch(error => {
                    hideLoadingIndicator();
                    console.error('Error fetching system health:', error);
                    alert('Error checking system health. Please try again.');
                });
        }

        function showSystemHealthModal(healthData) {
            const statusIcon = healthData.status === 'healthy' ? '✅' : '❌';
            const statusClass = healthData.status === 'healthy' ? 'text-success' : 'text-danger';

            const modalHtml = `
                <div class="modal fade" id="systemHealthModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">System Health Status</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-3">
                                    <h3 class="${statusClass}">${statusIcon} ${healthData.status.toUpperCase()}</h3>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Database:</strong>
                                        <span class="${healthData.database === 'healthy' ? 'text-success' : 'text-danger'}">
                                            ${healthData.database}
                                        </span>
                                    </div>
                                    <div class="col-6">
                                        <strong>Cache:</strong>
                                        <span class="${healthData.cache === 'healthy' ? 'text-success' : 'text-danger'}">
                                            ${healthData.cache}
                                        </span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <strong>Version:</strong> ${healthData.version}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" onclick="location.reload()">Refresh</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('systemHealthModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('systemHealthModal'));
            modal.show();
        }

        function showLoadingIndicator() {
            // Create or show loading indicator
            let loader = document.getElementById('globalLoader');
            if (!loader) {
                loader = document.createElement('div');
                loader.id = 'globalLoader';
                loader.innerHTML = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                         style="background: rgba(0,0,0,0.5); z-index: 9999;">
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `;
                document.body.appendChild(loader);
            }
            loader.style.display = 'block';
        }

        function hideLoadingIndicator() {
            const loader = document.getElementById('globalLoader');
            if (loader) {
                loader.style.display = 'none';
            }
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>